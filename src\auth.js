// auth.js
import NextAuth from "next-auth";
import { MongoDBAdapter } from "@auth/mongodb-adapter";
import Google from "next-auth/providers/google";
import Facebook from "next-auth/providers/facebook";
import Credentials from "next-auth/providers/credentials";
import Nodemailer from "next-auth/providers/nodemailer";
import { createTransport } from "nodemailer";
import clientPromise from "./libs/mongoDb/mongodb-client-promise"; // Import MongoDB client
import { comparePassword, hashPassword, generateToken } from "./libs/utils";

export const { handlers, signIn, signOut, auth } = NextAuth({
  // Use MongoDBAdapter for database persistence
  adapter: MongoDBAdapter(clientPromise),

  // Use JWT strategy for sessions (works better with credentials provider)
  session: {
    strategy: "jwt",
  },
  session: {
    strategy: "jwt", // Use JWT strategy for session management
    // JWT expiration can be configured here if needed
  },
  providers: [
    Google({
      clientId: process.env.GOOGLE_CLIENT_ID,
      clientSecret: process.env.GOOGLE_CLIENT_SECRET,
    }),
    Facebook({
      clientId: process.env.FACEBOOK_CLIENT_ID,
      clientSecret: process.env.FACEBOOK_CLIENT_SECRET,
    }),
    Credentials({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "email", placeholder: "<EMAIL>" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        const email = credentials.email;
        const password = credentials.password;

        try {
          // Get MongoDB client and database
          const client = await clientPromise;
          const db = client.db();

          // Find user by email in MongoDB
          const user = await db.collection("users").findOne({ email });

          if (user && user.password) {
            // Compare the provided password with the stored hashed password
            const isPasswordValid = await comparePassword(password, user.password);

            if (isPasswordValid) {
              // Return user object without sensitive information
              // NextAuth.js will handle mapping this to the session/JWT
              return {
                id: user._id.toString(),
                name: user.name,
                email: user.email,
                role: user.role
              };
            }
          }

          // If login fails, throw an error or return null
          throw new Error("Invalid credentials");
        } catch (error) {
          console.error("Authorization error:", error);
          throw new Error("Authentication failed");
        }
      },
    }),
    Nodemailer({
      server: {
        host: process.env.EMAIL_SERVER_HOST,
        port: parseInt(process.env.EMAIL_SERVER_PORT),
        auth: {
          user: process.env.EMAIL_SERVER_USER,
          pass: process.env.EMAIL_SERVER_PASSWORD,
        },
      },
      from: process.env.EMAIL_FROM,
      async sendVerificationRequest({ identifier: email, url, provider }) {
        const { host } = new URL(url);
        const transport = createTransport(provider.server);

        try {
          await transport.sendMail({
            to: email,
            from: provider.from,
            subject: `Sign in to ${host}`,
            html: `
              <div style="font-family: sans-serif; text-align: center; padding: 20px;">
                <h1 style="color: #333;">Sign in to ${host}</h1>
                <p>Click the link below to sign in:</p>
                <a href="${url}" style="display: inline-block; padding: 10px 20px; background-color: #3b82f6; color: white; text-decoration: none; border-radius: 5px;">
                  Sign in
                </a>
                <p>If you did not request this, you can safely ignore this email.</p>
              </div>
            `,
          });
          console.log(`Magic link sent to ${email}`);
        } catch (error) {
          console.error(`Failed to send magic link email to ${email}:`, error);
          throw new Error("Failed to send magic link email.");
        }
      },
    }),
  ],
  callbacks: {
    // Callback to fetch user details from MongoDB
    async signIn({ user, account, profile }) {
      console.log("signIn user object:", user);
      try {
        const client = await clientPromise;
        const db = client.db();

        const dbUser = await db.collection("users").findOne({ email: user.email });
        console.log("signIn dbUser:", dbUser); // Add this log

        if (!dbUser) {
          const newUser = {
            name: user.name,
            email: user.email,
            // For OAuth, you typically don't set a password here
            emailVerified: new Date(),
            image: user.image,
            createdAt: new Date(),
            updatedAt: new Date(),
            role: "user" // <--- Set a default role here for new OAuth users
          };
          await db.collection("users").insertOne(newUser);
          console.log("New user created in DB:", newUser.email);
        } else {
          // Update user's last login
          await db.collection("users").updateOne(
            { email: user.email },
            { $set: { lastLogin: new Date() } }
          );
          console.log("Existing user updated in DB:", user.email);
        }
        return true;
      } catch (error) {
        console.error("Error in signIn callback:", error);
        return false;
      }
    },
    // JWT callback is crucial for Credentials provider when using JWT strategy
    async jwt({ token, user, account }) {
      console.log('jwt',{token:token,user:user,account:account});
      if (user) {
        token.id = user.id;
        token.name = user.name;
        token.email = user.email;
        token.role = user.role;
      }
      // If a social login (account) is linked, ensure we pass that info to the token
      if (account) {
        token.accessToken = account.access_token;
      }
      return token;
    },
    // Session callback populates the session object client-side
    async session({ session, token }) {
      console.log('session',{session:session,token:token});
      if (token) {
        session.user.id = token.id;
        session.user.name = token.name;
        session.user.email = token.email;
        session.user.role = token.role;
        // If needed, expose access token or other account info
        // session.accessToken = token.accessToken;
      }
      return session;
    },
    // Optionally, you can add a redirect callback if you need custom redirect logic
    // async redirect({ url, baseUrl }) {
    //   // Allows relative callback URLs
    //   if (url.startsWith("/")) return `${baseUrl}${url}`
    //   // Allows absolute callback URLs for same origin
    //   else if (new URL(url).origin === baseUrl) return url
    //   return baseUrl
    // }
  },
  pages: {
    signIn: "/auth/signin",
    error: "/auth/signin?error=", // Redirect to login page on error, pass error message as query param
    verifyRequest: "/auth/signin?verifyRequest=true", // Redirect to login page after magic link email sent
  },
  events: {
    async signIn(message) {
      console.log("User signed in:", message.user.email);
    },
    async signOut(message) {
      console.log("User signed out:", message.session?.user?.email || message.token?.email);
    },
    async createUser(message) {
      console.log("User created:", message.user.email);
    },
    async linkAccount(message) {
      console.log("Account linked:", message.user.email, message.account.provider);
    },
  },
  debug: process.env.NODE_ENV === "development", // Enable debug logs in development
});

// --- Custom Password Reset & Registration Logic (Interacting with Mongoose) ---

/**
 * Initiates a password reset by sending a magic link email.
 * @param {string} email - The email of the user requesting a reset.
 * @returns {Promise<boolean>} True if email sent successfully, false otherwise.
 */
export async function sendPasswordResetEmail(email) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    const user = await db.collection("users").findOne({ email });

    if (!user) {
      console.log(`Password reset requested for non-existent email: ${email}`);
      return true; // Still return true to avoid leaking user existence
    }

    const token = generateToken();
    const expires = new Date(Date.now() + 3600 * 1000); // Token valid for 1 hour

    // Invalidate any existing reset tokens for this user
    await db.collection("verification_tokens").deleteMany({
      identifier: email,
      type: 'password_reset'
    });

    // Create a new password reset token in the verification_tokens collection
    await db.collection("verification_tokens").insertOne({
      identifier: email,
      token: token,
      expires: expires,
      type: 'password_reset', // Custom type to distinguish from magic links
    });

    const resetUrl = `${process.env.AUTH_URL}/auth/reset-password/${token}`;
    const transport = createTransport({
      host: process.env.EMAIL_SERVER_HOST,
      port: parseInt(process.env.EMAIL_SERVER_PORT),
      auth: {
        user: process.env.EMAIL_SERVER_USER,
        pass: process.env.EMAIL_SERVER_PASSWORD,
      },
    });

    await transport.sendMail({
      to: email,
      from: process.env.EMAIL_FROM,
      subject: `Password Reset for your account`,
      html: `
        <div style="font-family: sans-serif; text-align: center; padding: 20px;">
          <h1 style="color: #333;">Password Reset Request</h1>
          <p>You requested a password reset for your account.</p>
          <p>Click the link below to reset your password:</p>
          <a href="${resetUrl}" style="display: inline-block; padding: 10px 20px; background-color: #ef4444; color: white; text-decoration: none; border-radius: 5px;">
            Reset Password
          </a>
          <p>This link is valid for 1 hour.</p>
          <p>If you did not request this, you can safely ignore this email.</p>
        </div>
      `,
    });
    console.log(`Password reset email sent to ${email}`);
    return true;
  } catch (error) {
    console.error(`Failed to send password reset email to ${email}:`, error);
    return false;
  }
}

/**
 * Resets a user's password using a valid token.
 * @param {string} token - The password reset token.
 * @param {string} newPassword - The new plain text password.
 * @returns {Promise<boolean>} True if password was reset successfully, false otherwise.
 */
export async function resetUserPassword(token, newPassword) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    // Find the password reset token
    const resetToken = await db.collection("verification_tokens").findOne({
      token,
      type: 'password_reset'
    });

    if (!resetToken || resetToken.expires < new Date()) {
      console.warn("Invalid or expired password reset token.");
      return false;
    }

    const user = await db.collection("users").findOne({ email: resetToken.identifier });
    if (!user) {
      console.warn("User not found for password reset token.");
      return false;
    }

    const hashedPassword = await hashPassword(newPassword);

    // Update user password and mark email as verified
    await db.collection("users").updateOne(
      { _id: user._id },
      {
        $set: {
          password: hashedPassword,
          emailVerified: new Date()
        }
      }
    );

    // Invalidate token after use
    await db.collection("verification_tokens").deleteOne({ _id: resetToken._id });

    console.log(`Password for user ${user.email} reset successfully.`);
    return true;
  } catch (error) {
    console.error("Error resetting password:", error);
    return false;
  }
}

/**
 * Registers a new user with email and password.
 * This is a custom function, not part of NextAuth.js core providers.
 * @param {string} name
 * @param {string} email
 * @param {string} password
 * @returns {Promise<object|null>} The new user object if successful, null otherwise.
 */
export async function registerUser(name, email, password) {
  try {
    // Get MongoDB client and database
    const client = await clientPromise;
    const db = client.db();

    const existingUser = await db.collection("users").findOne({ email });

    if (existingUser) {
      return null; // User already exists
    }

    const hashedPassword = await hashPassword(password);
    const newUser = {
      name,
      email,
      password: hashedPassword,
      emailVerified: null,
      image: null,
      createdAt: new Date(),
      updatedAt: new Date()
    };

    const result = await db.collection("users").insertOne(newUser);
    return { ...newUser, id: result.insertedId.toString() };
  } catch (error) {
    console.error("Error registering user:", error);
    return null;
  }
}
