// middleware.js
// This middleware protects your application routes.
// It uses the `auth` function from auth.js to check session.

import { auth } from "./auth";

export default auth(async function middleware(req) {
  // `req.auth` is the user's session if authenticated.
  // console.log("Middleware auth:", req.auth);

  const { pathname } = req.nextUrl;

  // Define public routes that don't require authentication
  const publicRoutes = [
    "/signin",
    "/register",
    "/forgot-password",
    /^\/reset-password\/.+$/, // Matches /reset-password/some-token
    "/api/auth", // NextAuth.js API routes
  ];

  // Check if the current path is a public route
  const isPublicRoute = publicRoutes.some(route => {
    if (typeof route === "string") {
      return pathname.startsWith(route);
    } else if (route instanceof RegExp) {
      return route.test(pathname);
    }
    return false;
  });

  // If trying to access a protected route and not authenticated, redirect to login
  if (!req.auth && !isPublicRoute) {
    const url = req.nextUrl.clone();
    url.pathname = '/login';
    return Response.redirect(url);
  }

  // If authenticated and trying to access login/register, redirect to home
  if (req.auth && (pathname.startsWith("/signin") || pathname.startsWith("/register"))) {
    const url = req.nextUrl.clone();
    url.pathname = '/';
    return Response.redirect(url);
  }
});

// Configure the matcher to apply middleware to all routes except static files and API routes
export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico|.*\\.(?:png|jpg|jpeg|gif|webp|svg)$).*)"],
};
