import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import NavbarComponent from "@/components/NavbarComponent";
import AdminLink from "@/components/AdminLink";
import ErrorBoundary from "@/components/ErrorBoundary";
import { settings } from "@/libs/siteSettings";

// Initialize console error suppression in development
if (process.env.NODE_ENV === 'development') {
  import("@/utils/consoleErrorSuppression");
}

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata = {
  title: settings.siteName,
  description: settings.siteMaxim,
};

export default async function RootLayout({ children }) {

  return (
    <html lang="en" className="w-full h-screen">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased font-sans`}
      >
        <ErrorBoundary>
          <Providers>
            <NavbarComponent/>
            {children}
          </Providers>
          <AdminLink/>
        </ErrorBoundary>
      </body>
    </html>
  );
}
