import { Inter, JetBrains_Mono } from "next/font/google";
import "./globals.css";
import { Providers } from "./providers";
import NavbarComponent from "@/components/NavbarComponent";
import AdminLink from "@/components/AdminLink";
import ErrorBoundary from "@/components/ErrorBoundary";
import { settings } from "@/libs/siteSettings";
import { SessionProvider } from "next-auth/react";
import { auth } from "../auth";

// Initialize console error suppression in development
if (process.env.NODE_ENV === 'development') {
  import("@/utils/consoleErrorSuppression");
}

const inter = Inter({
  variable: "--font-inter",
  subsets: ["latin"],
  display: 'swap',
});

const jetbrainsMono = JetBrains_Mono({
  variable: "--font-jetbrains-mono",
  subsets: ["latin"],
  display: 'swap',
});

export const metadata = {
  title: settings.siteName,
  description: settings.siteMaxim,
};

export default async function RootLayout({ children }) {
  // Get the session on the server-side to pass to SessionProvider
  const session = await auth();
  return (
    <html lang="en" className="w-full h-screen">
      <body
        className={`${inter.variable} ${jetbrainsMono.variable} antialiased font-sans`}
      >
        <ErrorBoundary>
          <Providers>
            <NavbarComponent/>
            <SessionProvider session={session}>
            {children}
            </SessionProvider>
          </Providers>
          <AdminLink/>
        </ErrorBoundary>
      </body>
    </html>
  );
}
