// src/app/api/users/[id]/route.js
// API routes for individual user operations

import { NextResponse } from "next/server";
import { auth } from "../../../../auth";
import dbConnect from "../../../../libs/mongoDb/connectToLuyariDB";
import User from "../../../../libs/mongoDb/models/User";
import bcrypt from "bcryptjs";
import mongoose from "mongoose";

/**
 * GET /api/users/[id] - Get single user by ID (Admin only)
 */
export async function GET(request, { params }) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user has admin role or is requesting their own data
    if (session.user.role !== 'admin' && session.user.id !== params.id) {
      return NextResponse.json(
        { message: "Access denied" },
        { status: 403 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      );
    }

    const user = await User.findById(id).select('-password').lean();

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({ user });

  } catch (error) {
    console.error("Error fetching user:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/users/[id] - Update existing user (Admin only, or user updating themselves)
 */
export async function PUT(request, { params }) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      );
    }

    const updateData = await request.json();
    const isAdmin = session.user.role === 'admin';
    const isSelfUpdate = session.user.id === id;

    // Check permissions
    if (!isAdmin && !isSelfUpdate) {
      return NextResponse.json(
        { message: "Access denied" },
        { status: 403 }
      );
    }

    // Remove fields that shouldn't be updated
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    // Prevent non-admin users from changing role
    if (!isAdmin && updateData.role) {
      return NextResponse.json(
        { message: "Only admins can change user roles" },
        { status: 403 }
      );
    }

    // Prevent users from removing their own admin status
    if (isSelfUpdate && updateData.role && updateData.role !== 'admin' && session.user.role === 'admin') {
      return NextResponse.json(
        { message: "Cannot remove your own admin status" },
        { status: 403 }
      );
    }

    // Validate role if provided
    if (updateData.role) {
      const validRoles = ['user', 'admin'];
      if (!validRoles.includes(updateData.role)) {
        return NextResponse.json(
          { message: "Invalid role. Must be 'user' or 'admin'" },
          { status: 400 }
        );
      }
    }

    // Validate email format if provided
    if (updateData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updateData.email)) {
        return NextResponse.json(
          { message: "Invalid email format" },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const existingUser = await User.findOne({ 
        email: updateData.email,
        _id: { $ne: id }
      });
      
      if (existingUser) {
        return NextResponse.json(
          { message: "A user with this email already exists" },
          { status: 409 }
        );
      }
    }

    // Hash password if provided
    if (updateData.password) {
      if (updateData.password.length < 6) {
        return NextResponse.json(
          { message: "Password must be at least 6 characters" },
          { status: 400 }
        );
      }
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    const user = await User.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "User updated successfully",
      user: user.toObject()
    });

  } catch (error) {
    console.error("Error updating user:", error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { message: "A user with this email already exists" },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: "Validation error", errors: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/users/[id] - Update existing user (Admin only, or user updating themselves)
 */
export async function PATCH(request, { params }) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      );
    }

    const updateData = await request.json();
    const isAdmin = session.user.role === 'admin';
    const isSelfUpdate = session.user.id === id;

    // Check permissions
    if (!isAdmin && !isSelfUpdate) {
      return NextResponse.json(
        { message: "Access denied" },
        { status: 403 }
      );
    }

    // Remove fields that shouldn't be updated
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    // Prevent non-admin users from changing role
    if (!isAdmin && updateData.role) {
      return NextResponse.json(
        { message: "Only admins can change user roles" },
        { status: 403 }
      );
    }

    // Prevent users from removing their own admin status
    if (isSelfUpdate && updateData.role && updateData.role !== 'admin' && session.user.role === 'admin') {
      return NextResponse.json(
        { message: "Cannot remove your own admin status" },
        { status: 403 }
      );
    }

    // Validate role if provided
    if (updateData.role) {
      const validRoles = ['user', 'admin'];
      if (!validRoles.includes(updateData.role)) {
        return NextResponse.json(
          { message: "Invalid role. Must be 'user' or 'admin'" },
          { status: 400 }
        );
      }
    }

    // Validate email format if provided
    if (updateData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updateData.email)) {
        return NextResponse.json(
          { message: "Invalid email format" },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const existingUser = await User.findOne({ 
        email: updateData.email,
        _id: { $ne: id }
      });
      
      if (existingUser) {
        return NextResponse.json(
          { message: "A user with this email already exists" },
          { status: 409 }
        );
      }
    }

    // Hash password if provided
    if (updateData.password) {
      if (updateData.password.length < 6) {
        return NextResponse.json(
          { message: "Password must be at least 6 characters" },
          { status: 400 }
        );
      }
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    const user = await User.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "User updated successfully",
      user: user.toObject()
    });

  } catch (error) {
    console.error("Error updating user:", error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { message: "A user with this email already exists" },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: "Validation error", errors: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * PUT /api/users/[id] - Update existing user (Admin only, or user updating themselves)
 */
export async function PUT(request, { params }) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      );
    }

    const updateData = await request.json();
    const isAdmin = session.user.role === 'admin';
    const isSelfUpdate = session.user.id === id;

    // Check permissions
    if (!isAdmin && !isSelfUpdate) {
      return NextResponse.json(
        { message: "Access denied" },
        { status: 403 }
      );
    }

    // Remove fields that shouldn't be updated
    delete updateData._id;
    delete updateData.createdAt;
    delete updateData.updatedAt;

    // Prevent non-admin users from changing role
    if (!isAdmin && updateData.role) {
      return NextResponse.json(
        { message: "Only admins can change user roles" },
        { status: 403 }
      );
    }

    // Prevent users from removing their own admin status
    if (isSelfUpdate && updateData.role && updateData.role !== 'admin' && session.user.role === 'admin') {
      return NextResponse.json(
        { message: "Cannot remove your own admin status" },
        { status: 403 }
      );
    }

    // Validate role if provided
    if (updateData.role) {
      const validRoles = ['user', 'admin'];
      if (!validRoles.includes(updateData.role)) {
        return NextResponse.json(
          { message: "Invalid role. Must be 'user' or 'admin'" },
          { status: 400 }
        );
      }
    }

    // Validate email format if provided
    if (updateData.email) {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailRegex.test(updateData.email)) {
        return NextResponse.json(
          { message: "Invalid email format" },
          { status: 400 }
        );
      }

      // Check if email is already taken by another user
      const existingUser = await User.findOne({ 
        email: updateData.email,
        _id: { $ne: id }
      });
      
      if (existingUser) {
        return NextResponse.json(
          { message: "A user with this email already exists" },
          { status: 409 }
        );
      }
    }

    // Hash password if provided
    if (updateData.password) {
      if (updateData.password.length < 6) {
        return NextResponse.json(
          { message: "Password must be at least 6 characters" },
          { status: 400 }
        );
      }
      updateData.password = await bcrypt.hash(updateData.password, 10);
    }

    const user = await User.findByIdAndUpdate(
      id,
      { $set: updateData },
      { new: true, runValidators: true }
    ).select('-password');

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "User updated successfully",
      user: user.toObject()
    });

  } catch (error) {
    console.error("Error updating user:", error);
    
    if (error.code === 11000) {
      return NextResponse.json(
        { message: "A user with this email already exists" },
        { status: 409 }
      );
    }
    
    if (error.name === 'ValidationError') {
      const validationErrors = Object.values(error.errors).map(err => err.message);
      return NextResponse.json(
        { message: "Validation error", errors: validationErrors },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}

/**
 * DELETE /api/users/[id] - Delete user (Admin only)
 */
export async function DELETE(request, { params }) {
  try {
    const session = await auth();
    
    if (!session || !session.user) {
      return NextResponse.json(
        { message: "Authentication required" },
        { status: 401 }
      );
    }

    // Check if user has admin role
    if (session.user.role !== 'admin') {
      return NextResponse.json(
        { message: "Admin access required for deletion" },
        { status: 403 }
      );
    }

    await dbConnect();

    const { id } = params;

    // Validate ObjectId
    if (!mongoose.Types.ObjectId.isValid(id)) {
      return NextResponse.json(
        { message: "Invalid user ID" },
        { status: 400 }
      );
    }

    // Prevent admin from deleting themselves
    if (session.user.id === id) {
      return NextResponse.json(
        { message: "Cannot delete your own account" },
        { status: 403 }
      );
    }

    const user = await User.findByIdAndDelete(id).select('-password');

    if (!user) {
      return NextResponse.json(
        { message: "User not found" },
        { status: 404 }
      );
    }

    return NextResponse.json({
      message: "User deleted successfully",
      deletedUser: user.toObject()
    });

  } catch (error) {
    console.error("Error deleting user:", error);
    return NextResponse.json(
      { message: "Internal server error", error: error.message },
      { status: 500 }
    );
  }
}
