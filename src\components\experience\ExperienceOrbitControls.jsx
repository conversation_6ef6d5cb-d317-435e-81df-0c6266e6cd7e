'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import * as THREE from 'three';

export default function ExperienceOrbitControls({ data }) {
  const { experienceState } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const refControls = useRef(null);
  const { camera, scene } = useThree();
  const [snapObjects, setSnapObjects] = useState([]);
  const [snapObject, setSnapObject] = useState({});

  /**
   * Enhanced snap function with smooth transitions and proper orientation
   * Snaps camera to exact position and orientation of snappoint object
   */
  const handleSnapViewPoints = useCallback(() => {
    if (!refControls.current || !snapObjects || !camera) {
      console.warn("handleSnapViewPoints: Missing required parameters");
      return;
    }

    if (snapObjects) {
      const targetSnapPoint = snapObjects?.children?.find(({ name }) => {
        return name === experienceState?.activeRoomSnap;
      });

      if (targetSnapPoint) {
        let targetMesh = null;

        // Find the mesh within the snappoint
        targetSnapPoint.traverse((child) => {
          if (child.isMesh) {
            targetMesh = child;
          }
        });

        if (targetMesh) {
          setSnapObject(targetMesh);

          // Calculate camera position based on snappoint's orientation
          // Position camera at the snappoint's exact position
          const snapPointWorldPosition = new THREE.Vector3();
          targetMesh.getWorldPosition(snapPointWorldPosition);

          // Get the snappoint's forward direction (negative Z in local space)
          const snapPointForward = new THREE.Vector3(0, 0, -1);
          snapPointForward.applyQuaternion(targetMesh.quaternion);

          // Position camera slightly behind the snappoint in its forward direction
          const cameraOffset = experienceState?.firstPersonView
            ? new THREE.Vector3(0, 0.1, 0.05)  // Very close for first person
            : new THREE.Vector3(0, 0.5, 1);    // Further back for third person

          // Apply snappoint's rotation to the offset
          cameraOffset.applyQuaternion(targetMesh.quaternion);

          // Calculate final camera position
          const targetCameraPosition = snapPointWorldPosition.clone().add(cameraOffset);

          // Calculate look-at target (where the snappoint is facing)
          // Ensure the look-at target is at the same height as the camera for level viewing
          const lookAtDistance = experienceState?.firstPersonView ? 2 : 5;
          const lookAtTarget = new THREE.Vector3(
            targetCameraPosition.x + (snapPointForward.x * lookAtDistance),
            targetCameraPosition.y, // Keep same Y level as camera
            targetCameraPosition.z + (snapPointForward.z * lookAtDistance)
          );

          // Disable controls for 3 seconds during snap operation
          setControlsEnabled(false);

          // Directly set camera position and target for immediate snap
          camera.position.copy(targetCameraPosition);
          refControls.current.target.copy(lookAtTarget);
          refControls.current.update();

          // Re-enable controls after 3 seconds
          setTimeout(() => {
            setControlsEnabled(true);
            console.log("Controls re-enabled after 3 seconds");
          }, 3000);

          console.log("Snap completed to:", targetMesh.name);
          console.log("Camera position:", targetCameraPosition.toArray());
          console.log("Look-at target:", lookAtTarget.toArray());
          console.log("Controls enabled:", false, "-> will re-enable in 3 seconds");
        }
      }
    }
  }, [camera, experienceState?.activeRoomSnap, experienceState?.firstPersonView, snapObjects]);

  // Removed useFrame animation loop to prevent interference with OrbitControls

  /**
   * Handle activeRoomSnap changes
   */
  useEffect(() => {
    if (experienceState?.activeRoomSnap && snapObjects) {
      handleSnapViewPoints();
    } else {
      // Re-enable controls when no active snap
      setControlsEnabled(true);
    }
  }, [experienceState?.activeRoomSnap, snapObjects, handleSnapViewPoints]);

  /**
   * Initialize room snap objects on mount
   */
  useEffect(() => {
    const roomSnapsObjects = scene.getObjectByName('roomSnaps');
    setSnapObjects(roomSnapsObjects);

    // Set default camera position and target
    if (refControls.current) {
      refControls.current.target.copy(new THREE.Vector3(0, 0, 0));
      refControls.current.update();
    }
  }, [scene]);

  // Safe parsing of distance values from data
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 50);

  return (
    <>
      <OrbitControls
        ref={refControls}
        enabled={controlsEnabled}
        minDistance={experienceState?.firstPersonView ? 0 : minDistance}
        maxDistance={experienceState?.firstPersonView ? 0.5 : maxDistance}
        maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
        minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
        rotateSpeed={-0.25}
        enablePan={false}
        enableDamping={true}
        dampingFactor={0.05}
      />
    </>
  );
}