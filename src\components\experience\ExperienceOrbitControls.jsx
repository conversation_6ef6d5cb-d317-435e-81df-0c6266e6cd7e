'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree, useFrame } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import * as THREE from 'three';

export default function ExperienceOrbitControls({ data }) {
  const { experienceState } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const refControls = useRef(null);
  const { camera, scene, gl, invalidate  } = useThree();
  const [snapObjects, setSnapObjects] = useState([]);
  const [snapObject, setSnapObject] = useState({});

  const handleSnapViewPoints = () => {
    if(!refControls.current || !snapObjects) return;

    if(snapObjects){
      const targetSnapPoint=snapObjects?.children?.find(({name})=>{
        return name===experienceState?.activeRoomSnap
      })
      if(targetSnapPoint){
        targetSnapPoint.traverse((child) => {
          if (child.isMesh) {
            setSnapObject(child)
          }
        })
      }
    }
    if(snapObject && refControls.current){
      snapObject.position && refControls.current?.target?.copy(snapObject.position)
      snapObject.rotation && refControls.current?.object?.rotation?.copy(snapObject.rotation)
      refControls.current?.update()
    }
  }

  // const handleSnapViewPoints = () => {
  //   if(!refControls.current || !snapObjects) return;

  //   if(snapObjects){
  //     const targetSnapPoint=snapObjects?.children?.find(({name})=>{
  //       return name===experienceState?.activeRoomSnap
  //     })
  //     if(targetSnapPoint){
  //       targetSnapPoint.traverse((child) => {
  //         if (child.isMesh) {
  //           setSnapObject(child)
  //         }
  //       })
  //     }
  //   }
  //   if(snapObject && refControls.current){
  //     snapObject.position && refControls.current?.target?.copy(snapObject.position)
  //     snapObject.rotation && refControls.current?.object?.rotation?.copy(snapObject.rotation)
  //     refControls.current?.update()
  //   }
  // }
  
  useEffect(() => {
    handleSnapViewPoints()
  }, [experienceState?.activeRoomSnap,snapObjects])
  
  useEffect(() => {
    const roomSnapsObjects=scene.getObjectByName('roomSnaps')
    setSnapObjects(roomSnapsObjects)
    refControls.current?.target?.copy(new THREE.Vector3(0, 0, 0))
    refControls.current?.update()
  }, [])
  
  console.log('ExperienceControls snap controls:', refControls.current?.object);
  console.log('ExperienceControls snap object:', refControls.current?.target);

  // Safe parsing of distance values from data
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 50);

  return (
    <>
      <OrbitControls
        ref={refControls}
        // enabled={false}
        minDistance={experienceState?.firstPersonView ? 0 : minDistance}
        maxDistance={experienceState?.firstPersonView ? 0.5 : maxDistance}
        maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
        minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
        rotateSpeed={-0.25}
        enablePan={false}
        enableDamping={true}
        dampingFactor={0.05}
      />
    </>
  );
}