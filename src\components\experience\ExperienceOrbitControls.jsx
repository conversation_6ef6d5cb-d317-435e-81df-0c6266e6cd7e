'use client';
import React, { useState, useRef, useEffect, useCallback } from 'react';
import { OrbitControls } from '@react-three/drei';
import { degToRad } from 'three/src/math/MathUtils';
import { useThree, useFrame } from '@react-three/fiber';
import { useExperienceContext } from '@/libs/contextProviders/useExperienceContext';
import { useCameraSnapTracking } from '@/hooks/useCameraSnapTracking';
import * as THREE from 'three';

export default function ExperienceOrbitControls({ data }) {
  const { experienceState } = useExperienceContext();
  const [controlsEnabled, setControlsEnabled] = useState(true);
  const refControls = useRef(null);
  const { camera, scene } = useThree();
  const [snapObjects, setSnapObjects] = useState([]);
  const [snapObject, setSnapObject] = useState({});

  // Use custom hook for camera snap tracking
  const {
    isSnapping,
    hasObjectChanged,
    getTrackedObject,
    initializeSnap,
    updateSnapAnimation,
    clearTracking,
    calculateCameraPosition
  } = useCameraSnapTracking();

  /**
   * Enhanced snap function with smooth transitions and proper orientation
   * Snaps camera to exact position and orientation of snappoint object
   */
  const handleSnapViewPoints = useCallback(() => {
    if (!refControls.current || !snapObjects || !camera) {
      console.warn("handleSnapViewPoints: Missing required parameters");
      return;
    }

    if (snapObjects) {
      const targetSnapPoint = snapObjects?.children?.find(({ name }) => {
        return name === experienceState?.activeRoomSnap;
      });

      if (targetSnapPoint) {
        let targetMesh = null;

        // Find the mesh within the snappoint
        targetSnapPoint.traverse((child) => {
          if (child.isMesh) {
            targetMesh = child;
          }
        });

        if (targetMesh) {
          setSnapObject(targetMesh);

          // Calculate camera position based on snappoint's orientation
          // Position camera at the snappoint's exact position
          const snapPointWorldPosition = new THREE.Vector3();
          targetMesh.getWorldPosition(snapPointWorldPosition);

          // Get the snappoint's forward direction (negative Z in local space)
          const snapPointForward = new THREE.Vector3(0, 0, -1);
          snapPointForward.applyQuaternion(targetMesh.quaternion);

          // Position camera slightly behind the snappoint in its forward direction
          const cameraOffset = experienceState?.firstPersonView
            ? new THREE.Vector3(0, 0.1, 0.05)  // Very close for first person
            : new THREE.Vector3(0, 0.5, 1);    // Further back for third person

          // Apply snappoint's rotation to the offset
          cameraOffset.applyQuaternion(targetMesh.quaternion);

          // Calculate final camera position
          const targetCameraPosition = snapPointWorldPosition.clone().add(cameraOffset);

          // Calculate look-at target (where the snappoint is facing)
          const lookAtDistance = experienceState?.firstPersonView ? 2 : 5;
          const lookAtTarget = snapPointWorldPosition.clone().add(
            snapPointForward.multiplyScalar(lookAtDistance)
          );

          // Initialize smooth snap animation
          const success = initializeSnap(
            targetMesh,
            camera,
            refControls.current,
            cameraOffset
          );

          if (success) {
            // Disable controls for 3 seconds during snap operation
            setControlsEnabled(false);

            // Re-enable controls after 3 seconds
            setTimeout(() => {
              setControlsEnabled(true);
            }, 3000);

            console.log("Snap initiated to:", targetMesh.name, "at position:", targetCameraPosition.toArray());
          }
        }
      }
    }
  }, [camera, experienceState?.activeRoomSnap, experienceState?.firstPersonView, snapObjects, initializeSnap]);

  /**
   * Main animation loop - handles smooth transitions and object tracking
   */
  useFrame((_, delta) => {
    const controls = refControls.current;
    if (!controls) return;

    // Handle snap animation
    if (isSnapping()) {
      const snapCompleted = updateSnapAnimation(delta, camera, controls);

      if (snapCompleted) {
        // Snap animation completed, but keep controls disabled for full 3-second period
        console.log("Snap animation completed");
      }
    }
    // Track object changes when not actively snapping
    else {
      const trackedObject = getTrackedObject();
      if (trackedObject && hasObjectChanged(trackedObject)) {
        console.log("Tracked object has moved/rotated, updating camera position");

        // Determine offset based on current view mode
        const firstPersonOffset = new THREE.Vector3(0, 0.1, 0.05);
        const thirdPersonOffset = new THREE.Vector3(0, 0.5, 1);
        const offset = experienceState?.firstPersonView ? firstPersonOffset : thirdPersonOffset;

        // Re-initialize snap to follow the moved object
        const success = initializeSnap(trackedObject, camera, controls, offset);
        if (success) {
          setControlsEnabled(false);
          setTimeout(() => setControlsEnabled(true), 3000);
        }
      }
    }
  });

  /**
   * Handle activeRoomSnap changes
   */
  useEffect(() => {
    if (experienceState?.activeRoomSnap && snapObjects) {
      handleSnapViewPoints();
    } else {
      // Clear tracking when no active snap
      clearTracking();
      if (isSnapping()) {
        setControlsEnabled(true);
      }
    }
  }, [experienceState?.activeRoomSnap, snapObjects, handleSnapViewPoints, clearTracking, isSnapping]);

  /**
   * Initialize room snap objects on mount
   */
  useEffect(() => {
    const roomSnapsObjects = scene.getObjectByName('roomSnaps');
    setSnapObjects(roomSnapsObjects);

    // Set default camera position and target
    if (refControls.current) {
      refControls.current.target.copy(new THREE.Vector3(0, 0, 0));
      refControls.current.update();
    }
  }, [scene]);
  
  console.log('ExperienceControls snap controls:', refControls.current?.object);
  console.log('ExperienceControls snap object:', refControls.current?.target);

  // Safe parsing of distance values from data
  const minDistance = experienceState?.firstPersonView ? 0.0 : (parseFloat(data?.minDistance) || 1);
  const maxDistance = experienceState?.firstPersonView ? 0.5 : (parseFloat(data?.maxDistance) || 50);

  return (
    <>
      <OrbitControls
        ref={refControls}
        // enabled={false}
        minDistance={experienceState?.firstPersonView ? 0 : minDistance}
        maxDistance={experienceState?.firstPersonView ? 0.5 : maxDistance}
        maxPolarAngle={experienceState?.firstPersonView ? degToRad(135) : degToRad(85)}
        minPolarAngle={experienceState?.firstPersonView ? degToRad(45) : degToRad(0)}
        rotateSpeed={-0.25}
        enablePan={false}
        enableDamping={true}
        dampingFactor={0.05}
      />
    </>
  );
}