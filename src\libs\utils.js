// lib/utils.js
import bcrypt from 'bcrypt';

const SALT_ROUNDS = 10;

/**
 * Hashes a plain password using bcrypt.
 * @param {string} password - The plain text password.
 * @returns {Promise<string>} The hashed password.
 */
export async function hashPassword(password) {
  return bcrypt.hash(password, SALT_ROUNDS);
}

/**
 * Compares a plain password with a hashed password.
 * @param {string} plainPassword - The plain text password.
 * @param {string} hashedPassword - The hashed password from the database.
 * @returns {Promise<boolean>} True if passwords match, false otherwise.
 */
export async function comparePassword(plainPassword, hashedPassword) {
  return bcrypt.compare(plainPassword, hashedPassword);
}

/**
 * Generates a cryptographically secure random token.
 * @returns {string} A random UUID.
 */
export function generateToken() {
  return crypto.randomUUID();
}
